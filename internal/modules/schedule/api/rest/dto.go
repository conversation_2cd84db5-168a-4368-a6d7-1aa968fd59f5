package rest

import (
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
)

type turnResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	StartTime int        `json:"start_time"`
	EndTime   int        `json:"end_time"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

type scheduleResult struct {
	ID              string       `json:"id"`
	Name            string       `json:"name"`
	SessionDuration int          `json:"session_duration"`
	BreakDuration   int          `json:"break_duration"`
	Turns           []turnResult `json:"turns"`
	CreatedAt       *time.Time   `json:"created_at"`
	UpdatedAt       *time.Time   `json:"updated_at"`
	DeletedAt       *time.Time   `json:"deleted_at"`
}

func turnToResult(turn *model.Turn) turnResult {
	return turnResult{
		ID:        turn.ID,
		Name:      turn.Name,
		StartTime: turn.StartTime,
		EndTime:   turn.EndTime,
		CreatedAt: turn.CreatedAt,
		UpdatedAt: turn.UpdatedAt,
		DeletedAt: turn.DeletedAt,
	}
}

func scheduleToResult(schedule *model.Schedule) scheduleResult {
	var turns []turnResult
	for _, turn := range schedule.Turns {
		turns = append(turns, turnToResult(&turn))
	}

	return scheduleResult{
		ID:              schedule.ID,
		Name:            schedule.Name,
		SessionDuration: schedule.SessionDuration,
		BreakDuration:   schedule.BreakDuration,
		Turns:           turns,
		CreatedAt:       schedule.CreatedAt,
		UpdatedAt:       schedule.UpdatedAt,
		DeletedAt:       schedule.DeletedAt,
	}
}
