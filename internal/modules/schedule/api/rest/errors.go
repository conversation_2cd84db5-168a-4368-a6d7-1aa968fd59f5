package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ScheduleNotFoundCode:     http.StatusNotFound,
	model.TurnNotFoundCode:         http.StatusNotFound,
	model.ConflictScheduleNameCode: http.StatusConflict,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
