package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type turnCreateReq struct {
	Name      string `json:"name" validate:"required"`
	StartTime int    `json:"start_time" validate:"required"`
	EndTime   int    `json:"end_time" validate:"required"`
}

type scheduleCreate struct {
	Name            string          `json:"name" validate:"required"`
	SessionDuration int             `json:"session_duration" validate:"required"`
	BreakDuration   int             `json:"break_duration" validate:"required"`
	Turns           []turnCreateReq `json:"turns" validate:"required,dive"`
}

func scheduleCreateToModel(req scheduleCreate) model.ScheduleCreate {
	var turns []model.TurnCreate
	for _, turn := range req.Turns {
		turns = append(turns, model.TurnCreate{
			Name:      turn.Name,
			StartTime: turn.StartTime,
			EndTime:   turn.EndTime,
		})
	}

	return model.ScheduleCreate{
		Name:            req.Name,
		SessionDuration: req.SessionDuration,
		BreakDuration:   req.BreakDuration,
		Turns:           turns,
	}
}

// Create implements ScheduleHandler.
func (sh *scheduleHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[scheduleCreate](w, r, sh.validator)
	if err != nil {
		utils.LogErr(ctx, sh.log, err)
		return
	}

	id, err := sh.useCase.Create(ctx, scheduleCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, sh.log, err)
		respErrHandler(w, r, err, "Failed to create schedule")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
