package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

// Update implements model.ScheduleUsecase.
func (s *scheduleUsecase) Update(ctx context.Context, scheduleUpdate model.ScheduleUpdate) error {
	updatedSchedule := model.Schedule{
		ID:              scheduleUpdate.ID,
		Name:            scheduleUpdate.Name,
		SessionDuration: scheduleUpdate.SessionDuration,
		BreakDuration:   scheduleUpdate.BreakDuration,
	}

	// Create turns
	var turns []model.Turn
	for _, turnUpdate := range scheduleUpdate.Turns {
		turn := model.Turn{
			ID:        utils.UniqueId(),
			Name:      turnUpdate.Name,
			StartTime: turnUpdate.StartTime,
			EndTime:   turnUpdate.EndTime,
		}

		turns = append(turns, turn)
	}

	updatedSchedule.Turns = turns

	return s.repo.Update(ctx, updatedSchedule)
}
