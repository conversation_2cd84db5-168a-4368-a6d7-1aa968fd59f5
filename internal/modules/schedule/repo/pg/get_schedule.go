package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *schedulePostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Schedule, error) {
	var schedule model.Schedule

	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT id, name, session_duration, break_duration, created_at, updated_at, deleted_at
			FROM schedules
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&schedule.ID,
			&schedule.Name,
			&schedule.SessionDuration,
			&schedule.BreakDuration,
			&schedule.CreatedAt,
			&schedule.UpdatedAt,
			&schedule.DeletedAt,
		)

		if err != nil {
			return model.ScheduleNotFoundf(
				fmt.Sprintf("schedule with %s: %s not found", prop, value),
				err,
				nil,
			)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Fetch turns for this schedule
	err = pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		turnsQuery := `
			SELECT id, name, start_time, end_time, created_at, updated_at, deleted_at
			FROM turns
			WHERE schedule_id = $1 AND deleted_at IS NULL
			ORDER BY start_time ASC
		`

		rows, err := conn.Query(ctx, turnsQuery, schedule.ID)
		if err != nil {
			return utils.InternalErrorf("failed to get turns", err, nil)
		}
		defer rows.Close()

		var turns []model.Turn
		for rows.Next() {
			var turn model.Turn
			err := rows.Scan(
				&turn.ID,
				&turn.Name,
				&turn.StartTime,
				&turn.EndTime,
				&turn.CreatedAt,
				&turn.UpdatedAt,
				&turn.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan turn", err, nil)
			}
			turns = append(turns, turn)
		}

		schedule.Turns = turns
		return nil
	})

	if err != nil {
		return nil, err
	}

	return &schedule, nil
}
