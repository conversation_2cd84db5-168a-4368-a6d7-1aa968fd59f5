package model

import "github.com/JosueDiazC/schedhold-backend/internal/utils"

const (
	ScheduleNotFoundCode utils.ErrCode = utils.ScheduleCode + iota
	TurnNotFoundCode
	ConflictScheduleNameCode
)

func ScheduleNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ScheduleNotFoundCode, message, err, details)
}

func TurnNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(TurnNotFoundCode, message, err, details)
}

func ConflictScheduleNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ConflictScheduleNameCode, message, err, details)
}
