package rest

import (
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

type personResult struct {
	ID             string          `json:"id"`
	Name           string          `json:"name"`
	FatherLastName string          `json:"father_last_name"`
	MotherLastName string          `json:"mother_last_name"`
	Email          string          `json:"email"`
	Address        string          `json:"address"`
	Phone          string          `json:"phone"`
	BirthDate      *utils.DateOnly `json:"birth_date"`
	Gender         bool            `json:"gender"`
	Document       string          `json:"document"`
	DocumentType   int8            `json:"document_type"`
	CreatedAt      *time.Time      `json:"created_at"`
	UpdatedAt      *time.Time      `json:"updated_at"`
	DeletedAt      *time.Time      `json:"deleted_at"`
}

type workerResult struct {
	ID        string       `json:"id"`
	Person    personResult `json:"person"`
	Positions []int        `json:"positions"`
	CreatedAt *time.Time   `json:"created_at"`
	UpdatedAt *time.Time   `json:"updated_at"`
	DeletedAt *time.Time   `json:"deleted_at"`
}

func workerToResult(worker *model.Worker) workerResult {
	return workerResult{
		ID: worker.ID,
		Person: personResult{
			ID:             worker.Person.ID,
			Name:           worker.Person.Name,
			FatherLastName: worker.Person.FatherLastName,
			MotherLastName: worker.Person.MotherLastName,
			Email:          worker.Person.Email,
			Address:        worker.Person.Address,
			Phone:          worker.Person.Phone,
			BirthDate:      worker.Person.BirthDate,
			Gender:         worker.Person.Gender,
			Document:       worker.Person.Document,
			DocumentType:   worker.Person.DocumentType,
			CreatedAt:      worker.Person.CreatedAt,
			UpdatedAt:      worker.Person.UpdatedAt,
			DeletedAt:      worker.Person.DeletedAt,
		},
		Positions: worker.Positions,
		CreatedAt: worker.CreatedAt,
		UpdatedAt: worker.UpdatedAt,
		DeletedAt: worker.DeletedAt,
	}
}
