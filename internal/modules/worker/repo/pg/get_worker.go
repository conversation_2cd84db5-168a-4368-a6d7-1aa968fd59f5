package pg

import (
	"context"
	"fmt"
	"time"

	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *workerPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Worker, error) {
	var worker model.Worker
	var personID string

	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT id, person_id, positions, created_at, updated_at, deleted_at
			FROM workers
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&worker.ID,
			&personID,
			&worker.Positions,
			&worker.CreatedAt,
			&worker.UpdatedAt,
			&worker.DeletedAt,
		)

		if err != nil {
			return model.WorkerNotFoundf(
				fmt.Sprintf("worker with %s: %s not found", prop, value),
				err,
				nil,
			)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Fetch the person
	personQuery := `
		SELECT id, name, father_last_name, mother_last_name, email, address, phone, 
			birth_date, gender, document, document_type, created_at, updated_at, deleted_at
		FROM persons
		WHERE id = $1 AND deleted_at IS NULL
	`

	var person personModel.Person
	var birthDate *time.Time
	err = pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		row := conn.QueryRow(ctx, personQuery, personID)
		return row.Scan(
			&person.ID,
			&person.Name,
			&person.FatherLastName,
			&person.MotherLastName,
			&person.Email,
			&person.Address,
			&person.Phone,
			&birthDate,
			&person.Gender,
			&person.Document,
			&person.DocumentType,
			&person.CreatedAt,
			&person.UpdatedAt,
			&person.DeletedAt,
		)
	})

	if err != nil {
		return nil, err
	}

	if birthDate != nil {
		person.BirthDate = &utils.DateOnly{Time: *birthDate}
	}

	worker.Person = &person
	return &worker, nil
}
