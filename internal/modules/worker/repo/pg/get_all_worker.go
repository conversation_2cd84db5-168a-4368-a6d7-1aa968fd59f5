package pg

import (
	"context"
	"time"

	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *workerPostgreRepo) GetAll(ctx context.Context) ([]model.Worker, error) {
	var workers []model.Worker
	var personIDs []string

	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            SELECT id, person_id, positions, created_at, updated_at, deleted_at
            FROM workers
            WHERE deleted_at IS NULL
            ORDER BY created_at DESC
        `

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get workers", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var worker model.Worker
			var personID string

			err := rows.Scan(
				&worker.ID,
				&personID,
				&worker.Positions,
				&worker.CreatedAt,
				&worker.UpdatedAt,
				&worker.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan worker", err, nil)
			}

			workers = append(workers, worker)
			personIDs = append(personIDs, personID)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Fetch person data for each worker
	for i, personID := range personIDs {
		var person personModel.Person
		var birthDate *time.Time
		err = pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
			query := `
				SELECT id, name, father_last_name, mother_last_name, email, address, phone, 
					birth_date, gender, document, document_type, created_at, updated_at, deleted_at
				FROM persons
				WHERE id = $1 AND deleted_at IS NULL
			`

			row := conn.QueryRow(ctx, query, personID)
			return row.Scan(
				&person.ID,
				&person.Name,
				&person.FatherLastName,
				&person.MotherLastName,
				&person.Email,
				&person.Address,
				&person.Phone,
				&birthDate,
				&person.Gender,
				&person.Document,
				&person.DocumentType,
				&person.CreatedAt,
				&person.UpdatedAt,
				&person.DeletedAt,
			)
		})

		if err != nil {
			return nil, err
		}

		if birthDate != nil {
			person.BirthDate = &utils.DateOnly{Time: *birthDate}
		}

		workers[i].Person = &person
	}

	return workers, nil
}
