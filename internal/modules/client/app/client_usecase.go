package app

import (
    "context"

    personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
    "github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
)

type clientUsecase struct {
    repo     model.ClientRepository
    personUc personModel.PersonUsecase
}

// Delete implements model.ClientUsecase.
func (c *clientUsecase) Delete(ctx context.Context, id string) error {
    return c.repo.Delete(ctx, id)
}

// GetAll implements model.ClientUsecase.
func (c *clientUsecase) GetAll(ctx context.Context) ([]model.Client, error) {
    return c.repo.GetAll(ctx)
}

// GetByProp implements model.ClientUsecase.
func (c *clientUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Client, error) {
    return c.repo.GetByProp(ctx, prop, value)
}

func NewClientUsecase(repo model.ClientRepository, personUc personModel.PersonUsecase) model.ClientUsecase {
    return &clientUsecase{
        repo:     repo,
        personUc: personUc,
    }
}