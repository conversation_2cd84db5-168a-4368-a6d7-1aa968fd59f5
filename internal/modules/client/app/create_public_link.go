package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

// CreatePublicLink implements model.ClientUsecase.
func (c *clientUsecase) CreatePublicLink(ctx context.Context, link model.PublicClientLinkCreate) error {
	newLink := model.PublicClientLink{
		ID:         utils.UniqueId(),
		ScheduleID: link.ScheduleID,
		WorkerIDs:  link.WorkerIDs,
		ClientID:   link.ClientID,
		URL:        link.URL,
	}

	return c.repo.CreatePublicLink(ctx, newLink)
}
