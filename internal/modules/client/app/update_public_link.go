package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
)

// UpdatePublicLink implements model.ClientUsecase.
func (c *clientUsecase) UpdatePublicLink(ctx context.Context, link model.PublicClientLinkUpdate) error {
	updatedLink := model.PublicClientLink{
		ID:         link.ID,
		ScheduleID: link.ScheduleID,
		WorkerIDs:  link.WorkerIDs,
		ClientID:   link.ClientID,
		URL:        link.URL,
	}

	return c.repo.UpdatePublicLink(ctx, updatedLink)
}
