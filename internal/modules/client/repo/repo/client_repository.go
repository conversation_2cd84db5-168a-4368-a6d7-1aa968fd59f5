package repo

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/repo/pg"
)

type clientRepository struct {
	pgRepo pg.ClientPostgreRepo
}

// CountByProp implements model.ClientRepository.
func (c *clientRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return c.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.ClientRepository.
func (c *clientRepository) Create(ctx context.Context, client model.Client) error {
	return c.pgRepo.Create(ctx, client)
}

// Delete implements model.ClientRepository.
func (c *clientRepository) Delete(ctx context.Context, id string) error {
	return c.pgRepo.Delete(ctx, id)
}

// GetAll implements model.ClientRepository.
func (c *clientRepository) GetAll(ctx context.Context) ([]model.Client, error) {
	return c.pgRepo.GetAll(ctx)
}

// GetByProp implements model.ClientRepository.
func (c *clientRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Client, error) {
	return c.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.ClientRepository.
func (c *clientRepository) Update(ctx context.Context, client model.Client) error {
	return c.pgRepo.Update(ctx, client)
}

// CreatePublicLink implements model.ClientRepository.
func (c *clientRepository) CreatePublicLink(ctx context.Context, link model.PublicClientLink) error {
	return c.pgRepo.CreatePublicLink(ctx, link)
}

// UpdatePublicLink implements model.ClientRepository.
func (c *clientRepository) UpdatePublicLink(ctx context.Context, link model.PublicClientLink) error {
	return c.pgRepo.UpdatePublicLink(ctx, link)
}

// GetClientLink implements model.ClientRepository.
func (c *clientRepository) GetClientLink(ctx context.Context, url string) (*model.ClientLinkResponse, error) {
	return c.pgRepo.GetClientLink(ctx, url)
}

// GetPublicClientLinkByURL implements model.ClientRepository.
func (c *clientRepository) GetPublicClientLinkByURL(ctx context.Context, url string) (*model.PublicClientLink, error) {
	return c.pgRepo.GetPublicClientLinkByURL(ctx, url)
}

// DeletePublicLink implements model.ClientRepository.
func (c *clientRepository) DeletePublicLink(ctx context.Context, id string) error {
	return c.pgRepo.DeletePublicLink(ctx, id)
}

func NewClientRepository(pgRepo pg.ClientPostgreRepo) model.ClientRepository {
	return &clientRepository{
		pgRepo: pgRepo,
	}
}
