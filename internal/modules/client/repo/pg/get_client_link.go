package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) GetClientLink(ctx context.Context, url string) (*model.ClientLinkResponse, error) {
	var sessions []model.SessionInfo

	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Query to get all sessions of a client on the public_client_links schedule_id
		// Join sessions with turns to get schedule_id, then filter by public_client_links schedule_id and workers
		query := `
            SELECT s.id, s.client_id, s.worker_id, s.turn_id, s.day, s.time, s.created_at, s.updated_at, s.deleted_at
            FROM sessions s
            JOIN turns t ON s.turn_id = t.id AND t.deleted_at IS NULL
            JOIN public_client_links pcl ON t.schedule_id = pcl.schedule_id
                AND s.worker_id = ANY(pcl.worker_ids)
                AND s.client_id = pcl.client_id
                AND pcl.deleted_at IS NULL
            WHERE pcl.url = $1 AND s.deleted_at IS NULL
            ORDER BY s.day, s.time
        `

		rows, err := conn.Query(ctx, query, url)
		if err != nil {
			return utils.InternalErrorf("failed to query sessions for client link", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var session model.SessionInfo

			err := rows.Scan(
				&session.ID,
				&session.ClientID,
				&session.WorkerID,
				&session.TurnID,
				&session.Day,
				&session.Time,
				&session.CreatedAt,
				&session.UpdatedAt,
				&session.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan session", err, nil)
			}

			sessions = append(sessions, session)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	response := &model.ClientLinkResponse{
		Sessions: sessions,
	}

	return response, nil
}
