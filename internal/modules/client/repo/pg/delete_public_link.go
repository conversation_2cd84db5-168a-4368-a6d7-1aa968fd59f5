package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) DeletePublicLink(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            UPDATE public_client_links
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND deleted_at IS NULL
        `

		result, err := conn.Exec(ctx, query, id)
		if err != nil {
			return utils.InternalErrorf("failed to delete public client link", err, nil)
		}

		if result.RowsAffected() == 0 {
			return utils.NotFoundf(
				fmt.Sprintf("public client link with id: %s not found", id),
				fmt.Errorf("public client link not found or already deleted"),
				nil,
			)
		}

		return nil
	})
}
