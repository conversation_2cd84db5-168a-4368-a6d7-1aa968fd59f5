-- Migration: Replace turn_id with schedule_id in public_client_links table
-- This migration removes the turn_id column and adds schedule_id column

-- Remove foreign key constraint for turn_id
ALTER TABLE dev.public_client_links
DROP CONSTRAINT IF EXISTS fk_public_client_links_turn_id;

-- Remove the turn_id column
ALTER TABLE dev.public_client_links
DROP COLUMN IF EXISTS turn_id;

-- Add the schedule_id column with matching data type (VARCHAR(255) to match schedules.id)
ALTER TABLE dev.public_client_links
ADD COLUMN schedule_id VARCHAR(255) NOT NULL;

-- Add foreign key constraint for schedule_id
ALTER TABLE dev.public_client_links
ADD CONSTRAINT fk_public_client_links_schedule_id
FOREIGN KEY (schedule_id)
REFERENCES dev.schedules(id)
ON DELETE CASCADE;