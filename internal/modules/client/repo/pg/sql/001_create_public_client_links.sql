CREATE TABLE IF NOT EXISTS dev.public_client_links (
    id VARCHAR(255) PRIMARY KEY,
    schedule_id VARCHAR(255) NOT NULL,
    turn_id VARCHAR(255) NOT NULL,
    worker_ids VARCHAR(255)[] NOT NULL,
    url VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    CONSTRAINT fk_public_client_links_schedule_id
        FOREIGN KEY (schedule_id)
        REFERENCES dev.schedules(id)
        ON DELETE CASCADE,

    CONSTRAINT fk_public_client_links_turn_id
        FOREIGN KEY (turn_id)
        REFERENCES dev.turns(id)
        ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_public_client_links_deleted_at ON dev.public_client_links(deleted_at);

