package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ClientPostgreRepo interface {
	Create(ctx context.Context, client model.Client) error
	Update(ctx context.Context, client model.Client) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Client, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Client, error)
	Delete(ctx context.Context, id string) error

	// Public Client Link methods
	CreatePublicLink(ctx context.Context, link model.PublicClientLink) error
	UpdatePublicLink(ctx context.Context, link model.PublicClientLink) error
	GetClientLink(ctx context.Context, url string) (*model.ClientLinkResponse, error)
	GetPublicClientLinkByURL(ctx context.Context, url string) (*model.PublicClientLink, error)
	DeletePublicLink(ctx context.Context, id string) error
}

type clientPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewClientPostgreRepo(pool *pgxpool.Pool) ClientPostgreRepo {
	return &clientPostgreRepo{
		pool: pool,
	}
}
