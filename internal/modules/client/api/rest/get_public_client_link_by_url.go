package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

// GetPublicClientLinkByURL implements ClientHandler.
func (c *clientHandler) GetPublicClientLinkByURL(w http.ResponseWriter, r *http.Request) {
	url := r.PathValue("url")
	ctx := r.Context()

	if url == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("url path parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	link, err := c.useCase.GetPublicClientLinkByURL(ctx, url)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get public client link by URL")
		return
	}

	rest.SuccessDResponse(w, r, link, http.StatusOK)
}
