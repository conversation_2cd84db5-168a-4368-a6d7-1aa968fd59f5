package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

// GetClientLink implements ClientHandler.
func (c *clientHandler) GetClientLink(w http.ResponseWriter, r *http.Request) {
	url := r.PathValue("url")
	ctx := r.Context()

	if url == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("url path parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	response, err := c.useCase.GetClientLink(ctx, url)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get client link")
		return
	}

	rest.SuccessDResponse(w, r, response, http.StatusOK)
}
