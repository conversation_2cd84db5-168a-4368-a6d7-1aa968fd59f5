package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type ClientHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)

	// Public Client Link methods
	GeneratePublicLink(w http.ResponseWriter, r *http.Request)
	CreatePublicLink(w http.ResponseWriter, r *http.Request)
	UpdatePublicLink(w http.ResponseWriter, r *http.Request)
	GetClientLink(w http.ResponseWriter, r *http.Request)
	GetPublicClientLinkByURL(w http.ResponseWriter, r *http.Request)
	DeletePublicLink(w http.ResponseWriter, r *http.Request)
}

type clientHandler struct {
	useCase   model.ClientUsecase
	validator *validator.Validate
	log       *logrus.Logger
}

func NewClientHandler(
	useCase model.ClientUsecase,
	validator *validator.Validate,
	log *logrus.Logger,
) ClientHandler {
	return &clientHandler{
		useCase:   useCase,
		validator: validator,
		log:       log,
	}
}
