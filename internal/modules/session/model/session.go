package model

import (
	"time"

	clientModel "github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	workerModel "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
)

type Session struct {
	ID        string
	Client    *clientModel.Client
	Worker    *workerModel.Worker
	TurnID    string
	Day       int
	Time      int
	CreatedAt *time.Time
	UpdatedAt *time.Time
	DeletedAt *time.Time
}

type SessionCreate struct {
	ClientID string
	WorkerID string
	TurnID   string
	Day      int
	Time     int
}

type SessionUpdate struct {
	ID       string
	ClientID string
	WorkerID string
	TurnID   string
	Day      int
	Time     int
}
