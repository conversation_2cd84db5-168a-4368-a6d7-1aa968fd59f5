package rest

import (
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

type personResult struct {
	ID             string          `json:"id"`
	Name           string          `json:"name"`
	FatherLastName string          `json:"father_last_name"`
	MotherLastName string          `json:"mother_last_name"`
	Email          string          `json:"email"`
	Address        string          `json:"address"`
	Phone          string          `json:"phone"`
	BirthDate      *utils.DateOnly `json:"birth_date"`
	Gender         bool            `json:"gender"`
	Document       string          `json:"document"`
	DocumentType   int8            `json:"document_type"`
	CreatedAt      *time.Time      `json:"created_at"`
	UpdatedAt      *time.Time      `json:"updated_at"`
	DeletedAt      *time.Time      `json:"deleted_at"`
}

type clientResult struct {
	ID         string       `json:"id"`
	Person     personResult `json:"person"`
	PublicLink *string      `json:"public_link"`
	CreatedAt  *time.Time   `json:"created_at"`
	UpdatedAt  *time.Time   `json:"updated_at"`
	DeletedAt  *time.Time   `json:"deleted_at"`
}

type workerResult struct {
	ID        string       `json:"id"`
	Person    personResult `json:"person"`
	Positions []int        `json:"positions"`
	CreatedAt *time.Time   `json:"created_at"`
	UpdatedAt *time.Time   `json:"updated_at"`
	DeletedAt *time.Time   `json:"deleted_at"`
}

type sessionResult struct {
	ID        string        `json:"id"`
	Client    *clientResult `json:"client"`
	Worker    workerResult  `json:"worker"`
	TurnID    string        `json:"turn_id"`
	Day       int           `json:"day"`
	Time      int           `json:"time"`
	CreatedAt *time.Time    `json:"created_at"`
	UpdatedAt *time.Time    `json:"updated_at"`
	DeletedAt *time.Time    `json:"deleted_at"`
}

func sessionToResult(session *model.Session) sessionResult {
	result := sessionResult{
		ID:        session.ID,
		TurnID:    session.TurnID,
		Day:       session.Day,
		Time:      session.Time,
		CreatedAt: session.CreatedAt,
		UpdatedAt: session.UpdatedAt,
		DeletedAt: session.DeletedAt,
	}

	// Set client only if it exists
	if session.Client != nil {
		result.Client = &clientResult{
			ID: session.Client.ID,
			Person: personResult{
				ID:             session.Client.Person.ID,
				Name:           session.Client.Person.Name,
				FatherLastName: session.Client.Person.FatherLastName,
				MotherLastName: session.Client.Person.MotherLastName,
				Email:          session.Client.Person.Email,
				Address:        session.Client.Person.Address,
				Phone:          session.Client.Person.Phone,
				BirthDate:      session.Client.Person.BirthDate,
				Gender:         session.Client.Person.Gender,
				Document:       session.Client.Person.Document,
				DocumentType:   session.Client.Person.DocumentType,
				CreatedAt:      session.Client.Person.CreatedAt,
				UpdatedAt:      session.Client.Person.UpdatedAt,
				DeletedAt:      session.Client.Person.DeletedAt,
			},
			PublicLink: session.Client.PublicLink,
			CreatedAt:  session.Client.CreatedAt,
			UpdatedAt:  session.Client.UpdatedAt,
			DeletedAt:  session.Client.DeletedAt,
		}
	}

	result.Worker = workerResult{
		ID: session.Worker.ID,
		Person: personResult{
			ID:             session.Worker.Person.ID,
			Name:           session.Worker.Person.Name,
			FatherLastName: session.Worker.Person.FatherLastName,
			MotherLastName: session.Worker.Person.MotherLastName,
			Email:          session.Worker.Person.Email,
			Address:        session.Worker.Person.Address,
			Phone:          session.Worker.Person.Phone,
			BirthDate:      session.Worker.Person.BirthDate,
			Gender:         session.Worker.Person.Gender,
			Document:       session.Worker.Person.Document,
			DocumentType:   session.Worker.Person.DocumentType,
			CreatedAt:      session.Worker.Person.CreatedAt,
			UpdatedAt:      session.Worker.Person.UpdatedAt,
			DeletedAt:      session.Worker.Person.DeletedAt,
		},
		Positions: session.Worker.Positions,
		CreatedAt: session.Worker.CreatedAt,
		UpdatedAt: session.Worker.UpdatedAt,
		DeletedAt: session.Worker.DeletedAt,
	}

	return result
}
