package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type sessionCreateMany struct {
	Sessions []sessionCreate `json:"sessions" validate:"required,min=1,dive"`
}

func sessionCreateManyToModel(req sessionCreateMany) []model.SessionCreate {
	sessions := make([]model.SessionCreate, len(req.Sessions))
	for i, session := range req.Sessions {
		sessions[i] = sessionCreateToModel(session)
	}
	return sessions
}

// CreateMany implements SessionHandler.
func (s *sessionHandler) CreateMany(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[sessionCreateMany](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	ids, err := s.useCase.CreateMany(ctx, sessionCreateManyToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to create sessions")
		return
	}

	rest.SuccessDResponse(w, r, ids, http.StatusCreated)
}
