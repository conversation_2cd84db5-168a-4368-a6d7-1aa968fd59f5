package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

// GetByWorkerAndTurn implements SessionHandler.
func (s *sessionHandler) GetByWorkerAndTurn(w http.ResponseWriter, r *http.Request) {
	workerID := r.URL.Query().Get("worker_id")
	turnID := r.URL.Query().Get("turn_id")
	ctx := r.Context()

	if workerID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("worker_id query parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	if turnID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("turn_id query parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	sessions, err := s.useCase.GetByWorkerAndTurn(ctx, workerID, turnID)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get sessions by worker and turn")
		return
	}

	result := make([]sessionResult, len(sessions))
	for i, session := range sessions {
		result[i] = sessionToResult(&session)
	}

	rest.SuccessDResponse(w, r, result, http.StatusOK)
}
