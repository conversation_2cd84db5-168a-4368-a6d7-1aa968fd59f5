package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type sessionDeleteMany struct {
	IDs []string `json:"ids" validate:"required,min=1"`
}

// DeleteMany implements SessionHandler.
func (s *sessionHandler) DeleteMany(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[sessionDeleteMany](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	err = s.useCase.DeleteMany(ctx, req.IDs)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to delete sessions")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
