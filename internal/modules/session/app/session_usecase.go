package app

import (
	"context"

	clientModel "github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	workerModel "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
)

type sessionUsecase struct {
	repo     model.SessionRepository
	clientUc clientModel.ClientUsecase
	workerUc workerModel.WorkerUsecase
}

// Delete implements model.SessionUsecase.
func (s *sessionUsecase) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// GetAll implements model.SessionUsecase.
func (s *sessionUsecase) GetAll(ctx context.Context) ([]model.Session, error) {
	return s.repo.GetAll(ctx)
}

// GetByProp implements model.SessionUsecase.
func (s *sessionUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Session, error) {
	return s.repo.GetByProp(ctx, prop, value)
}

// GetByClientAndTurn implements model.SessionUsecase.
func (s *sessionUsecase) GetByClientAndTurn(ctx context.Context, clientID string, turnID string) ([]model.Session, error) {
	return s.repo.GetByClientAndTurn(ctx, clientID, turnID)
}

// GetByWorkerAndTurn implements model.SessionUsecase.
func (s *sessionUsecase) GetByWorkerAndTurn(ctx context.Context, workerID string, turnID string) ([]model.Session, error) {
	return s.repo.GetByWorkerAndTurn(ctx, workerID, turnID)
}

func NewSessionUsecase(repo model.SessionRepository, clientUc clientModel.ClientUsecase, workerUc workerModel.WorkerUsecase) model.SessionUsecase {
	return &sessionUsecase{
		repo:     repo,
		clientUc: clientUc,
		workerUc: workerUc,
	}
}
