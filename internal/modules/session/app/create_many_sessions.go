package app

import (
	"context"

	clientModel "github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	workerModel "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

// CreateMany implements model.SessionUsecase.
func (s *sessionUsecase) CreateMany(ctx context.Context, sessions []model.SessionCreate) ([]string, error) {
	if len(sessions) == 0 {
		return []string{}, nil
	}

	// Validate all sessions first
	for i, session := range sessions {
		// Validate that client exists (only if ClientID is not "0")
		if session.ClientID != "0" {
			_, err := s.clientUc.GetByProp(ctx, "id", session.ClientID)
			if err != nil {
				return nil, utils.InternalErrorf("validation failed for session %d", err, map[string]interface{}{
					"session_index": i,
					"client_id":     session.ClientID,
				})
			}
		}

		// Validate that worker exists
		_, err := s.workerUc.GetByProp(ctx, "id", session.WorkerID)
		if err != nil {
			return nil, utils.InternalErrorf("validation failed for session %d", err, map[string]interface{}{
				"session_index": i,
				"worker_id":     session.WorkerID,
			})
		}
	}

	// Create all session objects
	newSessions := make([]model.Session, len(sessions))
	ids := make([]string, len(sessions))

	for i, session := range sessions {
		id := utils.UniqueId()
		ids[i] = id

		newSession := model.Session{
			ID:     id,
			TurnID: session.TurnID,
			Day:    session.Day,
			Time:   session.Time,
		}

		// Set client only if ClientID is not "0"
		if session.ClientID != "0" {
			newSession.Client = &clientModel.Client{
				ID: session.ClientID,
			}
		}

		// Set worker
		newSession.Worker = &workerModel.Worker{
			ID: session.WorkerID,
		}

		newSessions[i] = newSession
	}

	// Create all sessions in one transaction
	err := s.repo.CreateMany(ctx, newSessions)
	if err != nil {
		return nil, err
	}

	return ids, nil
}
