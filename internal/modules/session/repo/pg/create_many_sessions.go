package pg

import (
	"context"
	"fmt"
	"strings"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *sessionPostgreRepo) CreateMany(ctx context.Context, sessions []model.Session) error {
	if len(sessions) == 0 {
		return nil
	}

	return pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Build the bulk insert query
		valueStrings := make([]string, 0, len(sessions))
		valueArgs := make([]any, 0, len(sessions)*6)

		for i, session := range sessions {
			valueStrings = append(valueStrings, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d)",
				i*6+1, i*6+2, i*6+3, i*6+4, i*6+5, i*6+6))

			// Handle client_id: if it's "0", save as NULL
			var clientID any
			if session.Client != nil && session.Client.ID != "0" {
				clientID = session.Client.ID
			}

			valueArgs = append(valueArgs,
				session.ID,
				clientID,
				session.Worker.ID,
				session.TurnID,
				session.Day,
				session.Time,
			)
		}

		query := fmt.Sprintf(`
			INSERT INTO sessions (id, client_id, worker_id, turn_id, day, time)
			VALUES %s
		`, strings.Join(valueStrings, ","))

		_, err := conn.Exec(ctx, query, valueArgs...)
		if err != nil {
			return utils.InternalErrorf("failed to create sessions", err, nil)
		}

		return nil
	})
}
