package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *sessionPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            UPDATE sessions
            SET deleted_at = NOW()
            WHERE id = $1 AND deleted_at IS NULL
        `

		result, err := conn.Exec(ctx, query, id)
		if err != nil {
			return utils.InternalErrorf("failed to delete session", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.SessionNotFoundf("session not found", nil, nil)
		}

		return nil
	})
}
