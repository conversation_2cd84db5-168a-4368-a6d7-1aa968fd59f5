package pg

import (
	"context"
	"fmt"
	"strings"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *sessionPostgreRepo) DeleteMany(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Build the placeholders for the IN clause
		placeholders := make([]string, len(ids))
		args := make([]any, len(ids))

		for i, id := range ids {
			placeholders[i] = fmt.Sprintf("$%d", i+1)
			args[i] = id
		}

		query := fmt.Sprintf(`
			UPDATE sessions
			SET deleted_at = NOW()
			WHERE id IN (%s) AND deleted_at IS NULL
		`, strings.Join(placeholders, ","))

		result, err := conn.Exec(ctx, query, args...)
		if err != nil {
			return utils.InternalErrorf("failed to delete sessions", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.SessionNotFoundf("no sessions found to delete", nil, nil)
		}

		return nil
	})
}
