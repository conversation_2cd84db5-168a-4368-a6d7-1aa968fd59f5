package pg

import (
	"context"
	"time"

	clientModel "github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	workerModel "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *sessionPostgreRepo) GetAll(ctx context.Context) ([]model.Session, error) {
	var sessions []model.Session
	var clientIDs, workerIDs []*string

	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            SELECT id, client_id, worker_id, turn_id, day, time, created_at, updated_at, deleted_at
            FROM sessions
            WHERE deleted_at IS NULL
            ORDER BY created_at DESC
        `

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get all sessions", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var session model.Session
			var clientID, workerID *string

			err := rows.Scan(
				&session.ID,
				&clientID,
				&workerID,
				&session.TurnID,
				&session.Day,
				&session.Time,
				&session.CreatedAt,
				&session.UpdatedAt,
				&session.DeletedAt,
			)

			if err != nil {
				return utils.InternalErrorf("failed to scan session", err, nil)
			}

			sessions = append(sessions, session)
			clientIDs = append(clientIDs, clientID)
			workerIDs = append(workerIDs, workerID)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Fetch client data for each session
	for i, clientID := range clientIDs {
		if clientID != nil {
			var client clientModel.Client
			var person personModel.Person
			var birthDate *time.Time
			err = pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
				query := `
					SELECT c.id, c.created_at, c.updated_at, c.deleted_at,
						p.id, p.name, p.father_last_name, p.mother_last_name, p.email, p.address, p.phone,
						p.birth_date, p.gender, p.document, p.document_type, p.created_at, p.updated_at, p.deleted_at
					FROM clients c
					JOIN persons p ON c.person_id = p.id
					WHERE c.id = $1 AND c.deleted_at IS NULL AND p.deleted_at IS NULL
				`
				row := conn.QueryRow(ctx, query, *clientID)
				return row.Scan(
					&client.ID,
					&client.CreatedAt,
					&client.UpdatedAt,
					&client.DeletedAt,
					&person.ID,
					&person.Name,
					&person.FatherLastName,
					&person.MotherLastName,
					&person.Email,
					&person.Address,
					&person.Phone,
					&birthDate,
					&person.Gender,
					&person.Document,
					&person.DocumentType,
					&person.CreatedAt,
					&person.UpdatedAt,
					&person.DeletedAt,
				)
			})

			if err != nil {
				return nil, utils.InternalErrorf("failed to get client for session", err, nil)
			}

			if birthDate != nil {
				person.BirthDate = &utils.DateOnly{Time: *birthDate}
			}
			client.Person = &person
			sessions[i].Client = &client
		} else {
			// Client ID is null, create placeholder client
			placeholderClient := clientModel.Client{
				ID:        "0",
				CreatedAt: nil,
				UpdatedAt: nil,
				DeletedAt: nil,
				Person: &personModel.Person{
					ID:             "0",
					Name:           "loading...",
					FatherLastName: "",
					MotherLastName: "",
					Email:          "",
					Address:        "",
					Phone:          "",
					BirthDate:      nil,
					Gender:         false,
					Document:       "",
					DocumentType:   1,
					CreatedAt:      nil,
					UpdatedAt:      nil,
					DeletedAt:      nil,
				},
			}
			sessions[i].Client = &placeholderClient
		}
	}

	// Fetch worker data for each session
	for i, workerID := range workerIDs {
		var worker workerModel.Worker
		var person personModel.Person
		var birthDate *time.Time
		err = pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
			query := `
				SELECT w.id, w.positions, w.created_at, w.updated_at, w.deleted_at,
					p.id, p.name, p.father_last_name, p.mother_last_name, p.email, p.address, p.phone,
					p.birth_date, p.gender, p.document, p.document_type, p.created_at, p.updated_at, p.deleted_at
				FROM workers w
				JOIN persons p ON w.person_id = p.id
				WHERE w.id = $1 AND w.deleted_at IS NULL AND p.deleted_at IS NULL
			`
			row := conn.QueryRow(ctx, query, *workerID)
			return row.Scan(
				&worker.ID,
				&worker.Positions,
				&worker.CreatedAt,
				&worker.UpdatedAt,
				&worker.DeletedAt,
				&person.ID,
				&person.Name,
				&person.FatherLastName,
				&person.MotherLastName,
				&person.Email,
				&person.Address,
				&person.Phone,
				&birthDate,
				&person.Gender,
				&person.Document,
				&person.DocumentType,
				&person.CreatedAt,
				&person.UpdatedAt,
				&person.DeletedAt,
			)
		})

		if err != nil {
			return nil, utils.InternalErrorf("failed to get worker for session", err, nil)
		}

		if birthDate != nil {
			person.BirthDate = &utils.DateOnly{Time: *birthDate}
		}
		worker.Person = &person
		sessions[i].Worker = &worker
	}

	return sessions, nil
}
