package repo

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/repo/pg"
)

type sessionRepository struct {
	pgRepo pg.SessionPostgreRepo
}

// CountByProp implements model.SessionRepository.
func (s *sessionRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return s.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.SessionRepository.
func (s *sessionRepository) Create(ctx context.Context, session model.Session) error {
	return s.pgRepo.Create(ctx, session)
}

// CreateMany implements model.SessionRepository.
func (s *sessionRepository) CreateMany(ctx context.Context, sessions []model.Session) error {
	return s.pgRepo.CreateMany(ctx, sessions)
}

// Delete implements model.SessionRepository.
func (s *sessionRepository) Delete(ctx context.Context, id string) error {
	return s.pgRepo.Delete(ctx, id)
}

// DeleteMany implements model.SessionRepository.
func (s *sessionRepository) DeleteMany(ctx context.Context, ids []string) error {
	return s.pgRepo.DeleteMany(ctx, ids)
}

// GetAll implements model.SessionRepository.
func (s *sessionRepository) GetAll(ctx context.Context) ([]model.Session, error) {
	return s.pgRepo.GetAll(ctx)
}

// GetByProp implements model.SessionRepository.
func (s *sessionRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Session, error) {
	return s.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.SessionRepository.
func (s *sessionRepository) Update(ctx context.Context, session model.Session) error {
	return s.pgRepo.Update(ctx, session)
}

// GetByClientAndTurn implements model.SessionRepository.
func (s *sessionRepository) GetByClientAndTurn(ctx context.Context, clientID string, turnID string) ([]model.Session, error) {
	return s.pgRepo.GetByClientAndTurn(ctx, clientID, turnID)
}

// GetByWorkerAndTurn implements model.SessionRepository.
func (s *sessionRepository) GetByWorkerAndTurn(ctx context.Context, workerID string, turnID string) ([]model.Session, error) {
	return s.pgRepo.GetByWorkerAndTurn(ctx, workerID, turnID)
}

func NewSessionRepository(pgRepo pg.SessionPostgreRepo) model.SessionRepository {
	return &sessionRepository{
		pgRepo: pgRepo,
	}
}
