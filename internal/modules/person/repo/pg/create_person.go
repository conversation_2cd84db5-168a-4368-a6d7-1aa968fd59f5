package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *personPostgreRepo) Create(ctx context.Context, person model.Person) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            INSERT INTO persons (id, name, father_last_name, mother_last_name, email, address, phone, birth_date, gender, document, document_type)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        `

		var birthDate interface{}
		if person.BirthDate != nil {
			birthDate = person.BirthDate.Time // Access the embedded time.Time
		}

		_, err := conn.Exec(ctx, query,
			person.ID,
			person.Name,
			person.FatherLastName,
			person.MotherLastName,
			person.Email,
			person.Address,
			person.Phone,
			birthDate,
			person.Gender,
			person.Document,
			person.DocumentType,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create person", err, nil)
		}

		return nil
	})
}
