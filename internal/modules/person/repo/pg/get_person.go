package pg

import (
	"context"
	"fmt"
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *personPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Person, error) {
	var person model.Person
	var birthDate *time.Time

	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
            SELECT id, name, father_last_name, mother_last_name, email, address, phone, 
                birth_date, gender, document, document_type, created_at, updated_at, deleted_at
            FROM persons
            WHERE %s = $1 AND deleted_at IS NULL
        `, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&person.ID,
			&person.Name,
			&person.FatherLastName,
			&person.MotherLastName,
			&person.Email,
			&person.Address,
			&person.Phone,
			&birthDate,
			&person.Gender,
			&person.Document,
			&person.DocumentType,
			&person.CreatedAt,
			&person.UpdatedAt,
			&person.DeletedAt,
		)

		if err != nil {
			return model.PersonNotFoundf(
				fmt.Sprintf("person with %s: %s not found", prop, value),
				err,
				nil,
			)
		}

		if birthDate != nil {
			person.BirthDate = &utils.DateOnly{Time: *birthDate}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &person, nil
}
