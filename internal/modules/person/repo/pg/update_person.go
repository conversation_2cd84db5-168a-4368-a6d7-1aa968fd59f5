package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *personPostgreRepo) Update(ctx context.Context, person model.Person) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            UPDATE persons
            SET name = $2, father_last_name = $3, mother_last_name = $4, email = $5, 
                address = $6, phone = $7, birth_date = $8, gender = $9, 
                document = $10, document_type = $11, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND deleted_at IS NULL
        `

		var birthDate interface{}
		if person.BirthDate != nil {
			birthDate = person.BirthDate.Time // Access the embedded time.Time
		}

		result, err := conn.Exec(ctx, query,
			person.ID,
			person.Name,
			person.FatherLastName,
			person.MotherLastName,
			person.Email,
			person.Address,
			person.Phone,
			birthDate,
			person.Gender,
			person.Document,
			person.DocumentType,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update person", err, nil)
		}

		if result.RowsAffected() == 0 {
			return model.PersonNotFoundf(
				fmt.Sprintf("person with id: %s not found", person.ID),
				fmt.Errorf("person not found or already deleted"),
				nil,
			)
		}

		return nil
	})
}
