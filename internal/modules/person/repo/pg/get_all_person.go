package pg

import (
	"context"
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *personPostgreRepo) GetAll(ctx context.Context) ([]model.Person, error) {
	var persons []model.Person

	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            SELECT id, name, father_last_name, mother_last_name, email, address, phone, 
                birth_date, gender, document, document_type, created_at, updated_at, deleted_at
            FROM persons
            WHERE deleted_at IS NULL
            ORDER BY created_at DESC
        `

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get persons", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var person model.Person
			var birthDate *time.Time

			err := rows.Scan(
				&person.ID,
				&person.Name,
				&person.FatherLastName,
				&person.MotherLastName,
				&person.Email,
				&person.Address,
				&person.Phone,
				&birthDate,
				&person.Gender,
				&person.Document,
				&person.DocumentType,
				&person.CreatedAt,
				&person.UpdatedAt,
				&person.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan person", err, nil)
			}

			if birthDate != nil {
				person.BirthDate = &utils.DateOnly{Time: *birthDate}
			}

			persons = append(persons, person)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return persons, nil
}
