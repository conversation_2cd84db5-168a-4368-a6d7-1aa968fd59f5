package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type PersonCreateReq struct {
	Name           string          `json:"name"`
	FatherLastName string          `json:"father_last_name"`
	MotherLastName string          `json:"mother_last_name"`
	Email          string          `json:"email"`
	Address        string          `json:"address"`
	Phone          string          `json:"phone"`
	BirthDate      *utils.DateOnly `json:"birth_date"`
	Gender         bool            `json:"gender"`
	Document       string          `json:"document"`
	DocumentType   int8            `json:"document_type"`
}

func PersonCreateToModel(person PersonCreateReq) model.PersonCreate {

	return model.PersonCreate{
		Name:           person.Name,
		FatherLastName: person.FatherLastName,
		MotherLastName: person.MotherLastName,
		Email:          person.Email,
		Address:        person.Address,
		Phone:          person.Phone,
		BirthDate:      person.BirthDate,
		Gender:         person.Gender,
		Document:       person.Document,
		DocumentType:   person.DocumentType,
	}
}

// Create implements PersonHandler.
func (p *personHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[PersonCreateReq](w, r, p.validator)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		return
	}

	id, err := p.useCase.Create(ctx, PersonCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to create person")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
