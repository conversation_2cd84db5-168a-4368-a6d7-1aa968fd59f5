package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type PersonUpdateRest struct {
	ID             string          `json:"id"`
	Name           string          `json:"name"`
	Father<PERSON>astName string          `json:"father_last_name"`
	MotherLastName string          `json:"mother_last_name"`
	Email          string          `json:"email"`
	Address        string          `json:"address"`
	Phone          string          `json:"phone"`
	BirthDate      *utils.DateOnly `json:"birth_date"`
	Gender         bool            `json:"gender"`
	Document       string          `json:"document"`
	DocumentType   int8            `json:"document_type"`
}

func PersonUpdateToModel(person PersonUpdateRest) model.PersonUpdate {
	return model.PersonUpdate{
		ID:             person.ID,
		Name:           person.Name,
		FatherLastName: person.FatherLastName,
		MotherLastName: person.MotherLastName,
		Email:          person.Email,
		Address:        person.Address,
		Phone:          person.Phone,
		BirthDate:      person.BirthDate,
		Gender:         person.Gender,
		Document:       person.Document,
		DocumentType:   person.DocumentType,
	}
}

// Update implements PersonHandler.
func (p *personHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[PersonUpdateRest](w, r, p.validator)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		return
	}

	err = p.useCase.Update(ctx, PersonUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to update person")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
