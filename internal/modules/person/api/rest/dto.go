package rest

import (
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

type personResult struct {
	ID             string          `json:"id"`
	Name           string          `json:"name"`
	Father<PERSON>astName string          `json:"father_last_name"`
	Mother<PERSON>astName string          `json:"mother_last_name"`
	Email          string          `json:"email"`
	Address        string          `json:"address"`
	Phone          string          `json:"phone"`
	BirthDate      *utils.DateOnly `json:"birth_date"`
	Gender         bool            `json:"gender"`
	Document       string          `json:"document"`
	DocumentType   int8            `json:"document_type"`
	CreatedAt      *time.Time      `json:"created_at"`
	UpdatedAt      *time.Time      `json:"updated_at"`
	DeletedAt      *time.Time      `json:"deleted_at"`
}

func personToResult(person *model.Person) personResult {
	return personResult{
		ID:             person.ID,
		Name:           person.Name,
		FatherLastName: person.FatherLastName,
		MotherLastName: person.MotherLastName,
		Email:          person.Email,
		Address:        person.Address,
		Phone:          person.Phone,
		BirthDate:      person.BirthDate,
		Gender:         person.Gender,
		Document:       person.Document,
		DocumentType:   person.DocumentType,
		CreatedAt:      person.CreatedAt,
		UpdatedAt:      person.UpdatedAt,
		DeletedAt:      person.DeletedAt,
	}
}
