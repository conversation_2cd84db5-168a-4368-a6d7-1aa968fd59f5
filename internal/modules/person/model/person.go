package model

import (
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

type Person struct {
	ID             string
	Name           string
	FatherLastName string
	MotherLastName string
	Email          string
	Address        string
	Phone          string
	BirthDate      *utils.DateOnly
	Gender         bool
	Document       string
	DocumentType   int8
	CreatedAt      *time.Time
	UpdatedAt      *time.Time
	DeletedAt      *time.Time
}

type PersonCreate struct {
	Name           string
	FatherLastName string
	MotherLastName string
	Email          string
	Address        string
	Phone          string
	BirthDate      *utils.DateOnly
	Gender         bool
	Document       string
	DocumentType   int8
}

type PersonUpdate struct {
	ID             string
	Name           string
	FatherLastName string
	MotherLastName string
	Email          string
	Address        string
	Phone          string
	BirthDate      *utils.DateOnly
	Gender         bool
	Document       string
	DocumentType   int8
}
