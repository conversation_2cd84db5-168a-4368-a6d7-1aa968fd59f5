package utils

import (
	"strings"
	"time"
)

type DateOnly struct {
	time.Time
}

func (d *DateOnly) UnmarshalJSON(data []byte) error {
	// Remove quotes from JSON string
	dateStr := strings.Trim(string(data), `"`)

	// Handle empty/null values
	if dateStr == "" || dateStr == "null" {
		return nil
	}

	// Parse date-only format
	parsedTime, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return err
	}

	d.Time = parsedTime
	return nil
}

func (d DateOnly) MarshalJSON() ([]byte, error) {
	if d.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte(`"` + d.Time.Format("2006-01-02") + `"`), nil
}
