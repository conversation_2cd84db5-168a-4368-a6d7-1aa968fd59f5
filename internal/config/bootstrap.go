package config

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/api"

	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/go-playground/validator/v10"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	userRest "github.com/JosueDiazC/schedhold-backend/internal/modules/user/api/rest"
	userUsecase "github.com/JosueDiazC/schedhold-backend/internal/modules/user/app"
	userPg "github.com/JosueDiazC/schedhold-backend/internal/modules/user/repo/pg"
	userRepo "github.com/JosueDiazC/schedhold-backend/internal/modules/user/repo/repo"

	personRest "github.com/JosueDiazC/schedhold-backend/internal/modules/person/api/rest"
	personUsecase "github.com/JosueDiazC/schedhold-backend/internal/modules/person/app"
	personPg "github.com/JosueDiazC/schedhold-backend/internal/modules/person/repo/pg"
	personRepo "github.com/JosueDiazC/schedhold-backend/internal/modules/person/repo/repo"

	restMdlwr "github.com/JosueDiazC/schedhold-backend/internal/services/middleware/rest"

	authRest "github.com/JosueDiazC/schedhold-backend/internal/modules/auth/api/rest"
	authUsecase "github.com/JosueDiazC/schedhold-backend/internal/modules/auth/app"
	authPg "github.com/JosueDiazC/schedhold-backend/internal/modules/auth/repo/pg"
	authRepo "github.com/JosueDiazC/schedhold-backend/internal/modules/auth/repo/repo"

	workerRest "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/api/rest"
	workerUsecase "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/app"
	workerPg "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/repo/pg"
	workerRepo "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/repo/repo"

	clientRest "github.com/JosueDiazC/schedhold-backend/internal/modules/client/api/rest"
	clientApp "github.com/JosueDiazC/schedhold-backend/internal/modules/client/app"
	clientPg "github.com/JosueDiazC/schedhold-backend/internal/modules/client/repo/pg"
	clientRepo "github.com/JosueDiazC/schedhold-backend/internal/modules/client/repo/repo"

	scheduleRest "github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/api/rest"
	scheduleApp "github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/app"
	schedulePg "github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/repo/pg"
	scheduleRepo "github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/repo/repo"

	sessionRest "github.com/JosueDiazC/schedhold-backend/internal/modules/session/api/rest"
	sessionApp "github.com/JosueDiazC/schedhold-backend/internal/modules/session/app"
	sessionPg "github.com/JosueDiazC/schedhold-backend/internal/modules/session/repo/pg"
	sessionRepo "github.com/JosueDiazC/schedhold-backend/internal/modules/session/repo/repo"
)

type BootstrapConfig struct {
	HTTP     *http.ServeMux
	Log      *logrus.Logger
	Validate *validator.Validate
	Config   *viper.Viper
	PgDB     *pgxpool.Pool
}

func Bootstrap(config *BootstrapConfig) {
	utils.Init()

	userPg := userPg.NewUserPostgreRepo(config.PgDB)
	userRepo := userRepo.NewUserRepository(userPg)
	userUsecase := userUsecase.NewUserUsecase(userRepo)
	userHandler := userRest.NewUserHandler(config.Log, config.Validate, userUsecase)

	personPg := personPg.NewPersonPostgreRepo(config.PgDB)
	personRepo := personRepo.NewPersonRepository(personPg)
	personUsecase := personUsecase.NewPersonUsecase(personRepo)
	personHandler := personRest.NewPersonHandler(config.Log, config.Validate, personUsecase)

	restMdlwr := restMdlwr.NewHTTPMiddleware(config.Log, config.Config, userUsecase)

	authPg := authPg.NewAuthPostgreRepo(config.PgDB)
	authRepo := authRepo.NewAuthRepository(authPg)
	authUsecase := authUsecase.NewAuthUsecase(authRepo)
	authHandler := authRest.NewAuthHandler(config.Log, config.Validate, authUsecase)

	workerPg := workerPg.NewWorkerPostgreRepo(config.PgDB)
	workerRepo := workerRepo.NewWorkerRepository(workerPg)
	workerUsecase := workerUsecase.NewWorkerUsecase(workerRepo, personUsecase)
	workerHandler := workerRest.NewWorkerHandler(workerUsecase, config.Validate, config.Log)

	// Client module
	clientPg := clientPg.NewClientPostgreRepo(config.PgDB)
	clientRepo := clientRepo.NewClientRepository(clientPg)
	clientUsecase := clientApp.NewClientUsecase(clientRepo, personUsecase)
	clientHandler := clientRest.NewClientHandler(clientUsecase, config.Validate, config.Log)

	// Schedule module
	schedulePg := schedulePg.NewSchedulePostgreRepo(config.PgDB)
	scheduleRepo := scheduleRepo.NewScheduleRepository(schedulePg)
	scheduleUsecase := scheduleApp.NewScheduleUsecase(scheduleRepo)
	scheduleHandler := scheduleRest.NewScheduleHandler(scheduleUsecase, config.Validate, config.Log)

	// Session module
	sessionPg := sessionPg.NewSessionPostgreRepo(config.PgDB)
	sessionRepo := sessionRepo.NewSessionRepository(sessionPg)
	sessionUsecase := sessionApp.NewSessionUsecase(sessionRepo, clientUsecase, workerUsecase)
	sessionHandler := sessionRest.NewSessionHandler(sessionUsecase, config.Validate, config.Log)

	routeConfig := api.NewRouteConfig(
		config.HTTP,
		config.Log,
		restMdlwr,
		userHandler,
		authHandler,
		personHandler,
		workerHandler,
		clientHandler,
		scheduleHandler,
		sessionHandler,
	)

	routeConfig.SetupRoutes()
}
